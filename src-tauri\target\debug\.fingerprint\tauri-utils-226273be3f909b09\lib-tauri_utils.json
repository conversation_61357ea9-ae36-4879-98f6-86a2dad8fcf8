{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 1576101622367071602, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 11173404461216673582], [3060637413840920116, "proc_macro2", false, 17423250190087945303], [3150220818285335163, "url", false, 11562839756506472223], [3191507132440681679, "serde_untagged", false, 7204953779698888715], [4899080583175475170, "semver", false, 17343531450092427673], [5578504951057029730, "serde_with", false, 4398907414582564159], [5986029879202738730, "log", false, 5354761331429757099], [6606131838865521726, "ctor", false, 3111066180042418224], [6913375703034175521, "schemars", false, 2190022138773458741], [7170110829644101142, "json_patch", false, 13346418495005281353], [8319709847752024821, "uuid", false, 12848664522573018126], [9010263965687315507, "http", false, 19658731293818321], [9451456094439810778, "regex", false, 11954208443263387047], [9556762810601084293, "brotli", false, 13700087193540667202], [9689903380558560274, "serde", false, 17474093151178861523], [10806645703491011684, "thiserror", false, 4794224307179080737], [11655476559277113544, "cargo_metadata", false, 12560664836097286608], [11989259058781683633, "dunce", false, 5162970056373538091], [13625485746686963219, "anyhow", false, 14624950242237906499], [14232843520438415263, "html5ever", false, 1417532407973189042], [15088007382495681292, "kuchiki", false, 7885971374724127288], [15367738274754116744, "serde_json", false, 4045233045729652620], [15609422047640926750, "toml", false, 6922706566891061230], [15622660310229662834, "walkdir", false, 11329612567648749023], [15932120279885307830, "memchr", false, 7806156119520509878], [17146114186171651583, "infer", false, 9111458318137236277], [17155886227862585100, "glob", false, 9107329985247477000], [17186037756130803222, "phf", false, 12806676061687248893], [17990358020177143287, "quote", false, 11683726084642195291]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-226273be3f909b09\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}