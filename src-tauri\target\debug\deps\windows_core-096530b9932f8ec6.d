D:\dev\videoide\src-tauri\target\debug\deps\windows_core-096530b9932f8ec6.d: D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\lib.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\mod.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\can_into.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\com_bindings.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\ref_count.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\sha1.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\weak_ref_count.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\as_impl.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\com_object.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\guid.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\inspectable.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\interface.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\out_param.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\out_ref.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\param.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\param_value.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\ref.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\runtime_name.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\runtime_type.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\scoped_interface.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\type.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\unknown.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\weak.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\windows.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\agile_reference.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\array.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\event.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\handles.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\variant.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\windows.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\factory_cache.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\generic_factory.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\waiter.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\bindings.rs

D:\dev\videoide\src-tauri\target\debug\deps\libwindows_core-096530b9932f8ec6.rlib: D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\lib.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\mod.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\can_into.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\com_bindings.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\ref_count.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\sha1.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\weak_ref_count.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\as_impl.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\com_object.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\guid.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\inspectable.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\interface.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\out_param.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\out_ref.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\param.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\param_value.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\ref.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\runtime_name.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\runtime_type.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\scoped_interface.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\type.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\unknown.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\weak.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\windows.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\agile_reference.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\array.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\event.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\handles.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\variant.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\windows.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\factory_cache.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\generic_factory.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\waiter.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\bindings.rs

D:\dev\videoide\src-tauri\target\debug\deps\libwindows_core-096530b9932f8ec6.rmeta: D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\lib.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\mod.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\can_into.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\com_bindings.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\ref_count.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\sha1.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\weak_ref_count.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\as_impl.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\com_object.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\guid.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\inspectable.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\interface.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\out_param.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\out_ref.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\param.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\param_value.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\ref.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\runtime_name.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\runtime_type.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\scoped_interface.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\type.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\unknown.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\weak.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\windows.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\agile_reference.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\array.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\event.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\handles.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\variant.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\windows.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\factory_cache.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\generic_factory.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\waiter.rs D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\bindings.rs

D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\lib.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\mod.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\can_into.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\com_bindings.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\ref_count.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\sha1.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\weak_ref_count.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\as_impl.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\com_object.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\guid.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\inspectable.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\interface.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\out_param.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\out_ref.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\param.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\param_value.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\ref.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\runtime_name.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\runtime_type.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\scoped_interface.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\type.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\unknown.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\weak.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\windows.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\agile_reference.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\array.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\event.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\handles.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\variant.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\windows.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\factory_cache.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\generic_factory.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\waiter.rs:
D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\windows-core-0.58.0\src\imp\bindings.rs:
