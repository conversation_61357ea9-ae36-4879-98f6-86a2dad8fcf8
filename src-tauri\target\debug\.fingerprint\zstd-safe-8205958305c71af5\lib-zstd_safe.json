{"rustc": 1842507548689473721, "features": "[\"std\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 13834647262792939399, "profile": 4758648406898424494, "path": 2426292180931543228, "deps": [[8373447648276846408, "zstd_sys", false, 11255224809975606460], [15788444815745660356, "build_script_build", false, 2405894065524115647]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zstd-safe-8205958305c71af5\\dep-lib-zstd_safe", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}