{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"thin\", \"thin-lto\", \"wasm\", \"zdict_builder\", \"zstdmt\"]", "target": 13967053409313941148, "profile": 15657897354478470176, "path": 6732253361884296059, "deps": [[15788444815745660356, "zstd_safe", false, 9204456383164970984]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zstd-e554db9edcadc7ea\\dep-lib-zstd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}