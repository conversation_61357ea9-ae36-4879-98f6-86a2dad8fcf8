{"rustc": 1842507548689473721, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 11987593577772629573, "path": 10282370533200856735, "deps": [[2013030631243296465, "webview2_com", false, 8009150793477564317], [3334271191048661305, "windows_version", false, 3368675882760042441], [3722963349756955755, "once_cell", false, 15455555880537896548], [4143744114649553716, "raw_window_handle", false, 8758997598796580990], [5628259161083531273, "windows_core", false, 17545345177425444251], [7606335748176206944, "dpi", false, 18227596368208838549], [9010263965687315507, "http", false, 19658731293818321], [9141053277961803901, "build_script_build", false, 12693011749854729026], [10806645703491011684, "thiserror", false, 4794224307179080737], [11989259058781683633, "dunce", false, 5162970056373538091], [14585479307175734061, "windows", false, 6878485785883966654], [16727543399706004146, "cookie", false, 5354399545107149664]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-8c07836d04e9e0ae\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}