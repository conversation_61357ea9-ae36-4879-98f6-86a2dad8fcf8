{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 10240568608642146247, "deps": [[654232091421095663, "tauri_utils", false, 2964264746515725903], [4899080583175475170, "semver", false, 17343531450092427673], [6913375703034175521, "schemars", false, 2190022138773458741], [7170110829644101142, "json_patch", false, 13346418495005281353], [9689903380558560274, "serde", false, 17474093151178861523], [12714016054753183456, "tauri_winres", false, 1293176710572627759], [13077543566650298139, "heck", false, 8645442593182876745], [13475171727366188400, "cargo_toml", false, 9389304130737905157], [13625485746686963219, "anyhow", false, 14624950242237906499], [15367738274754116744, "serde_json", false, 4045233045729652620], [15609422047640926750, "toml", false, 6922706566891061230], [15622660310229662834, "walkdir", false, 11329612567648749023], [16928111194414003569, "dirs", false, 10885823997799391768], [17155886227862585100, "glob", false, 9107329985247477000]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-00e664f5d7ce14e2\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}