{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 1550442458081311219, "deps": [[654232091421095663, "tauri_utils", false, 2964264746515725903], [2704937418414716471, "tauri_codegen", false, 15849500344420362705], [3060637413840920116, "proc_macro2", false, 17423250190087945303], [4974441333307933176, "syn", false, 2116388865398820018], [13077543566650298139, "heck", false, 8645442593182876745], [17990358020177143287, "quote", false, 11683726084642195291]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-1df9377750f6133d\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}