{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 266013487267336723, "deps": [[376837177317575824, "softbuffer", false, 10600892787291139868], [654232091421095663, "tauri_utils", false, 1024297069367706748], [2013030631243296465, "webview2_com", false, 8009150793477564317], [3150220818285335163, "url", false, 12101033925353838191], [3722963349756955755, "once_cell", false, 15455555880537896548], [4143744114649553716, "raw_window_handle", false, 8758997598796580990], [5986029879202738730, "log", false, 5354761331429757099], [8826339825490770380, "tao", false, 13585840384398748638], [9010263965687315507, "http", false, 19658731293818321], [9141053277961803901, "wry", false, 2825123362214781740], [12304025191202589669, "build_script_build", false, 9196162610147474893], [12943761728066819757, "tauri_runtime", false, 10368671653276801149], [14585479307175734061, "windows", false, 6878485785883966654]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-e268edbd93bfa9a4\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}