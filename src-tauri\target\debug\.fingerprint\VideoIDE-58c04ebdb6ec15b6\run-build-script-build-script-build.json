{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[18251603632334587267, "build_script_build", false, 16695386513357076160], [12092653563678505622, "build_script_build", false, 6836755872232525198], [3834743577069889284, "build_script_build", false, 7575288737779842721], [422130612855741759, "build_script_build", false, 8170409660924880118], [17218623086136245857, "build_script_build", false, 8094681395224102611], [13496694572208715981, "build_script_build", false, 7456055978321552949]], "local": [{"RerunIfChanged": {"output": "debug\\build\\VideoIDE-58c04ebdb6ec15b6\\output", "paths": ["tauri.conf.json", "capabilities", "lib\\ffmpeg\\ffmpeg.exe", "lib\\ffmpeg\\ffprobe.exe", "lib\\mpv\\libmpv-2.dll"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}