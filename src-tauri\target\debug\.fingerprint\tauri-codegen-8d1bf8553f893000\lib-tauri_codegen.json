{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 3831501143037367577, "deps": [[654232091421095663, "tauri_utils", false, 2964264746515725903], [3060637413840920116, "proc_macro2", false, 17423250190087945303], [3150220818285335163, "url", false, 11562839756506472223], [4899080583175475170, "semver", false, 17343531450092427673], [4974441333307933176, "syn", false, 2116388865398820018], [7170110829644101142, "json_patch", false, 13346418495005281353], [7392050791754369441, "ico", false, 3120985067296682026], [8319709847752024821, "uuid", false, 12848664522573018126], [9556762810601084293, "brotli", false, 13700087193540667202], [9689903380558560274, "serde", false, 17474093151178861523], [9857275760291862238, "sha2", false, 16886311224569318632], [10806645703491011684, "thiserror", false, 4794224307179080737], [12687914511023397207, "png", false, 12871215205031842330], [13077212702700853852, "base64", false, 3113183951275580874], [15367738274754116744, "serde_json", false, 4045233045729652620], [15622660310229662834, "walkdir", false, 11329612567648749023], [17990358020177143287, "quote", false, 11683726084642195291]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-8d1bf8553f893000\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}