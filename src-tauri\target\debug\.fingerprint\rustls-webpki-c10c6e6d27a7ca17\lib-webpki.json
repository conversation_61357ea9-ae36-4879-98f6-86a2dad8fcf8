{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 15657897354478470176, "path": 4862106509718381626, "deps": [[2883436298747778685, "pki_types", false, 13009756648464907770], [5491919304041016563, "ring", false, 10914780049016818015], [8995469080876806959, "untrusted", false, 13824287357772631535]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-webpki-c10c6e6d27a7ca17\\dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}