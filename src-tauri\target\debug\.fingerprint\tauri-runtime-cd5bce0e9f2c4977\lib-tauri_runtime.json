{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 1995976849762621589, "deps": [[654232091421095663, "tauri_utils", false, 1024297069367706748], [3150220818285335163, "url", false, 12101033925353838191], [4143744114649553716, "raw_window_handle", false, 8758997598796580990], [7606335748176206944, "dpi", false, 18227596368208838549], [9010263965687315507, "http", false, 19658731293818321], [9689903380558560274, "serde", false, 13917790453763015151], [10806645703491011684, "thiserror", false, 4794224307179080737], [12943761728066819757, "build_script_build", false, 17884493727853822448], [14585479307175734061, "windows", false, 6878485785883966654], [15367738274754116744, "serde_json", false, 11096967495204620297], [16727543399706004146, "cookie", false, 5354399545107149664]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-cd5bce0e9f2c4977\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}