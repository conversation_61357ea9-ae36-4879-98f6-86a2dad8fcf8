{"rustc": 1842507548689473721, "features": "[\"log\", \"logging\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 11320133501219741205, "deps": [[2883436298747778685, "pki_types", false, 13009756648464907770], [3722963349756955755, "once_cell", false, 15455555880537896548], [5491919304041016563, "ring", false, 10914780049016818015], [5986029879202738730, "log", false, 5354761331429757099], [6528079939221783635, "zeroize", false, 4527574463037586397], [16400140949089969347, "build_script_build", false, 10405072109631049258], [17003143334332120809, "subtle", false, 4150683454270747732], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 3921680728051331528]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-2d4346efb48934da\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}